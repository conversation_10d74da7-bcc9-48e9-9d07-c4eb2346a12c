'use strict';
const bcrypt = require('bcryptjs');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Get the platinumuganda tenant ID
    const tenants = await queryInterface.sequelize.query(
      `SELECT id FROM tenant WHERE sub = 'platinumuganda'`,
      { type: Sequelize.QueryTypes.SELECT }
    );

    if (tenants.length === 0) {
      console.log('Platinum Uganda tenant not found. Please run the tenant seeder first.');
      return;
    }

    const tenantId = tenants[0].id;

    // Hash passwords
    const salt = bcrypt.genSaltSync(10);
    const hashPassword = (password) => bcrypt.hashSync(password, salt);

    // Sample users for platinumuganda
    const users = [ 
      {
        username: 'admin',
        email: '<EMAIL>',
        password: hashPassword('Admin@123'),
        sub: 'platinumuganda',
        subId: tenantId.toString(),
        employerId: 'EMP001',
        role: 'systemAdmin',
        status: 'active',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        username: 'hr',
        email: '<EMAIL>',
        password: hashPassword('Hr@123'),
        sub: 'platinumuganda',
        subId: tenantId.toString(),
        employerId: 'EMP002',
        role: 'hr',
        status: 'active',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        username: 'it',
        email: '<EMAIL>',
        password: hashPassword('It@123'),
        sub: 'platinumuganda',
        subId: tenantId.toString(),
        employerId: 'EMP003',
        role: 'it',
        status: 'active',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        username: 'supervisor',
        email: '<EMAIL>',
        password: hashPassword('Supervisor@123'),
        sub: 'platinumuganda',
        subId: tenantId.toString(),
        employerId: 'EMP004',
        role: 'supervisor',
        status: 'active',
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];

    // Check for existing users to avoid duplicates
    for (const user of users) {
      const existingUser = await queryInterface.sequelize.query(
        `SELECT * FROM "user" WHERE email = '${user.email}'`,
        { type: Sequelize.QueryTypes.SELECT }
      );

      if (existingUser.length === 0) {
        await queryInterface.bulkInsert('user', [user]);
        console.log(`User ${user.username} created successfully for Platinum Uganda`);
      } else {
        console.log(`User ${user.username} already exists for Platinum Uganda, skipping creation`);
      }
    }
  },

  async down(queryInterface, Sequelize) {
    // Get the platinumuganda tenant ID
    const tenants = await queryInterface.sequelize.query(
      `SELECT id FROM tenant WHERE sub = 'platinumuganda'`,
      { type: Sequelize.QueryTypes.SELECT }
    );

    if (tenants.length === 0) {
      return;
    }

    const tenantId = tenants[0].id;

    // Delete the users for platinumuganda
    await queryInterface.bulkDelete('user', { 
      subId: tenantId.toString() 
    });
  }
};
