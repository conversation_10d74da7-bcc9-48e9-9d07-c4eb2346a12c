const { UserAccessRequest } = require('./src/db/models');

async function checkPhoneData() {
  try {
    console.log('🔍 Checking all records with phone number +256772656545...');
    
    const records = await UserAccessRequest.findAll({
      where: {
        telephone: '+256772656545'
      },
      attributes: ['id', 'firstName', 'lastName', 'telephone', 'subId', 'approvalStatus', 'createdAt'],
      order: [['createdAt', 'DESC']]
    });

    console.log(`Found ${records.length} records:`);
    
    records.forEach((record, index) => {
      console.log(`\n📋 Record ${index + 1}:`);
      console.log(`  ID: ${record.id}`);
      console.log(`  Name: ${record.firstName} ${record.lastName}`);
      console.log(`  Phone: ${record.telephone}`);
      console.log(`  Subsidiary: ${record.subId}`);
      console.log(`  Status: ${record.approvalStatus}`);
      console.log(`  Created: ${record.createdAt}`);
    });

    console.log('\n🔍 Checking all records with similar phone patterns...');
    
    const similarRecords = await UserAccessRequest.findAll({
      where: {
        telephone: {
          [require('sequelize').Op.like]: '%772656545%'
        }
      },
      attributes: ['id', 'firstName', 'lastName', 'telephone', 'subId', 'approvalStatus', 'createdAt'],
      order: [['createdAt', 'DESC']]
    });

    console.log(`Found ${similarRecords.length} similar records:`);
    
    similarRecords.forEach((record, index) => {
      console.log(`\n📋 Similar Record ${index + 1}:`);
      console.log(`  ID: ${record.id}`);
      console.log(`  Name: ${record.firstName} ${record.lastName}`);
      console.log(`  Phone: ${record.telephone}`);
      console.log(`  Subsidiary: ${record.subId}`);
      console.log(`  Status: ${record.approvalStatus}`);
      console.log(`  Created: ${record.createdAt}`);
    });

  } catch (error) {
    console.error('❌ Error checking phone data:', error);
  } finally {
    process.exit(0);
  }
}

checkPhoneData();
