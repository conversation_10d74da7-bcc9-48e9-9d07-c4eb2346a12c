const express = require("express");
const router = express.Router();

const { uploadSignature, uploadExcel } = require("../_middlewares/upload");

const {
  submitRevocation,
  getPendingRevocations,
  approveRevocation,
  rejectRevocation,
  getAllRevocations,
  getCompletedRevocations,
  bulkUploadRevocations,
} = require("../controllers/revocationFormController");

// HR submits revocation form with signature (single)
router.post("/submit", uploadSignature.single("signature"), submitRevocation);

// HR bulk uploads Excel file (with error handling)
router.post("/bulk-upload", uploadExcel, bulkUploadRevocations);

// Filtered: Get all pending revocations for a specific subsidiary
router.get("/pending", (req, res) => {
  const subsidiary = req.headers["x-subsidiary"];
  getPendingRevocations(req, res, subsidiary);
});

// IT approves specific revocation
router.put("/:id/approve", approveRevocation);

// IT rejects specific revocation
router.put("/:id/reject", rejectRevocation);

// Filtered: Get full audit trail per subsidiary
router.get("/audit", (req, res) => {
  const subsidiary = req.headers["x-subsidiary"];
  getAllRevocations(req, res, subsidiary);
});

// Filtered: Get completed revocations for subsidiary
router.get("/completed", (req, res) => {
  const subsidiary = req.headers["x-subsidiary"];
  getCompletedRevocations(req, res, subsidiary);
});

module.exports = router;
