const { Revocation } = require("../db/models");
const XLSX = require("xlsx");
const fs = require("fs");
const path = require('path');
const subsidiaryEmailConfig = require('../config/subsidiaryEmailConfig');

// Submit single revocation
const submitRevocation = async (req, res) => {
  try {
    const {
      fullName,
      employeeId,
      jobTitle,
      department,
      contact,
      systems,
      reason,
      otherReason,
      approverName,
      approverJobTitle,
      approverContact,
      subsidiary,
    } = req.body;

    const parsedSystems =
      typeof systems === "string" ? JSON.parse(systems) : systems;

    const signaturePath = req.file ? req.file.path : null;

    const revocation = await Revocation.create({
      fullName,
      employeeId,
      jobTitle,
      department,
      contact,
      systems: parsedSystems,
      reason,
      otherReason,
      approverName,
      approverJobTitle,
      approverContact,
      signaturePath,
      subsidiary,
      status: "Pending", // Simple workflow - goes directly to IT
      submittedBy: req.user?.username || "HR", // Track who submitted
    });

    res.status(201).json({
      message: "Revocation submitted successfully",
      data: revocation,
    });
  } catch (error) {
    console.error("Revocation error:", error);
    res.status(500).json({ error: "Failed to submit revocation" });
  }
};

// Get all pending revocations (filtered by subsidiary if provided)
const getPendingRevocations = async (req, res, subsidiary = null) => {
  try {
    const where = { status: "Pending" };
    if (subsidiary) where.subsidiary = subsidiary;

    const pending = await Revocation.findAll({
      where,
      order: [["createdAt", "DESC"]],
    });
    res.status(200).json({ data: pending });
  } catch (error) {
    console.error("Error fetching pending revocations:", error);
    res.status(500).json({ error: "Failed to fetch pending revocations" });
  }
};

// HR approves revocation (first step)
const approveRevocationHR = async (req, res) => {
  try {
    const { id } = req.params;
    const revocation = await Revocation.findByPk(id);
    if (!revocation) return res.status(404).json({ error: "Revocation not found" });

    if (revocation.status !== "Pending") {
      return res.status(400).json({
        error: "Invalid status",
        message: "Revocation must be in Pending status for HR approval"
      });
    }

    // Update status to HR approved
    revocation.status = "Approved by HR";
    await revocation.save();

    // Send email notification to IT
    const emailService = require('../utils/emailService');
    const { approverEmails } = require('../config/approverEmails');

    const subsidiary = revocation.subsidiary;
    const approvers = approverEmails[subsidiary];

    if (approvers && approvers.IT) {
      try {
        await emailService.sendEmail({
          to: approvers.IT,
          subject: "Access Revocation Pending IT Approval",
          tenant: subsidiary,
          emailBody: `
            <p>Dear IT Team,</p>
            <p>A revocation request has been approved by HR and is now pending your final approval.</p>
            <table style="border-collapse: collapse; width: auto;">
              <tr><td style="font-weight: bold;">Employee:</td><td>${revocation.fullName}</td></tr>
              <tr><td style="font-weight: bold;">Employee ID:</td><td>${revocation.employeeId}</td></tr>
              <tr><td style="font-weight: bold;">Department:</td><td>${revocation.department}</td></tr>
              <tr><td style="font-weight: bold;">Systems:</td><td>${revocation.systems.join(", ")}</td></tr>
              <tr><td style="font-weight: bold;">Reason:</td><td>${revocation.reason}</td></tr>
            </table>
            <p><a href="https://uat-uap.platcorpgroup.com/user-access/ui/removal/${subsidiary}" target="_blank">Click here to review and approve this revocation</a></p>
            <p>Best regards,<br>${subsidiary} Access Management System</p>
          `,
        });
        console.log(`HR approval email sent to IT: ${approvers.IT}`);
      } catch (emailError) {
        console.error("Error sending HR approval email:", emailError);
        // Don't fail the approval if email fails
      }
    }

    res.status(200).json({
      message: "Revocation approved by HR and forwarded to IT",
      data: revocation
    });
  } catch (error) {
    console.error("Error in HR approval:", error);
    res.status(500).json({ error: "Failed to approve revocation at HR level" });
  }
};

// IT approves revocation (final step)
const approveRevocation = async (req, res) => {
  try {
    const { id } = req.params;
    const revocation = await Revocation.findByPk(id);
    if (!revocation) return res.status(404).json({ error: "Revocation not found" });

    if (revocation.status !== "Approved by HR") {
      return res.status(400).json({
        error: "Invalid status",
        message: "Revocation must be approved by HR first"
      });
    }

    revocation.status = "Approved";
    revocation.actionedAt = new Date();
    revocation.actionedByIT = req.user?.username || "IT ADMIN";

    await revocation.save();
    res.status(200).json({ message: "Revocation fully approved by IT", data: revocation });
  } catch (error) {
    console.error("Error approving revocation:", error);
    res.status(500).json({ error: "Failed to approve revocation" });
  }
};

// IT rejects revocation
const rejectRevocation = async (req, res) => {
  try {
    const { id } = req.params;
    const { rejectionReason } = req.body;
    const revocation = await Revocation.findByPk(id);
    if (!revocation) return res.status(404).json({ error: "Not found" });

    revocation.status = "Rejected";
    revocation.rejectionReason = rejectionReason;
    revocation.actionedAt = new Date();
    revocation.actionedByIT = "IT ADMIN";

    await revocation.save();
    res.status(200).json({ message: "Revocation rejected", data: revocation });
  } catch (error) {
    console.error("Error rejecting revocation:", error);
    res.status(500).json({ error: "Failed to reject revocation" });
  }
};

// Get full audit trail (filtered by subsidiary if provided)
const getAllRevocations = async (req, res, subsidiary = null) => {
  try {
    const where = {};
    if (subsidiary) where.subsidiary = subsidiary;

    const all = await Revocation.findAll({
      where,
      order: [["createdAt", "DESC"]],
    });
    res.status(200).json({ data: all });
  } catch (error) {
    console.error("Error fetching all revocations:", error);
    res.status(500).json({ error: "Failed to fetch audit records" });
  }
};

// Get completed revocations (Approved or Rejected, filtered by subsidiary if provided)
const getCompletedRevocations = async (req, res, subsidiary = null) => {
  try {
    const where = {
      status: ["Approved", "Rejected"],
    };
    if (subsidiary) where.subsidiary = subsidiary;

    const completed = await Revocation.findAll({
      where,
      order: [["actionedAt", "DESC"]],
    });
    res.status(200).json({ data: completed });
  } catch (error) {
    console.error("Error fetching completed revocations:", error);
    res.status(500).json({ error: "Failed to fetch processed revocations" });
  }
};

// BULK UPLOAD revocations + Email IT Admin

// Import the email service
const emailService = require('../utils/emailService');

const bulkUploadRevocations = async (req, res) => {
  try {
    console.log("Bulk upload request received");
    console.log("Request body:", req.body);
    console.log("Request file:", req.file ? {
      fieldname: req.file.fieldname,
      originalname: req.file.originalname,
      mimetype: req.file.mimetype,
      size: req.file.size,
      path: req.file.path
    } : 'No file');

    // Check if file exists
    if (!req.file) {
      console.error("No file uploaded in request");
      return res.status(400).json({ error: "No file uploaded" });
    }

    const filePath = req.file.path;

    // Check if file exists on disk
    if (!fs.existsSync(filePath)) {
      console.error(`File not found at path: ${filePath}`);
      return res.status(400).json({ error: "Uploaded file not found on server" });
    }

    // Check file size
    const stats = fs.statSync(filePath);
    if (stats.size === 0) {
      console.error("Uploaded file is empty");
      return res.status(400).json({ error: "Uploaded file is empty" });
    }

    // Read the Excel file
    let workbook;
    try {
      workbook = XLSX.readFile(filePath);
    } catch (error) {
      console.error("Error reading Excel file:", error);
      return res.status(400).json({ error: "Invalid Excel file format" });
    }

    // Check if workbook has sheets
    if (!workbook.SheetNames || workbook.SheetNames.length === 0) {
      console.error("Excel file has no sheets");
      return res.status(400).json({ error: "Excel file has no sheets" });
    }

    const sheet = workbook.Sheets[workbook.SheetNames[0]];
    const rows = XLSX.utils.sheet_to_json(sheet);

    console.log(`Parsed ${rows.length} rows from Excel file`);

    // Get subsidiary from request
    const subsidiary = req.body.subsidiary || req.headers["x-subsidiary"] || "Unknown";
    console.log(`Using subsidiary: ${subsidiary}`);

    const revocations = [];

    // Process each row
    for (const row of rows) {
      console.log("Processing row:", row);

      // Extract and format the full name
      const fullName =
        `${row["First Name"] || ""} ${row["Middle Name"] || ""} ${row["Surname"] || ""}`.trim();
      const employeeId = row["Payroll No."] || "";

      if (!fullName || !employeeId) {
        console.log(`Skipping row with missing name or ID: ${JSON.stringify(row)}`);
        continue;
      }

      try {
        const entry = await Revocation.create({
          fullName,
          employeeId,
          jobTitle: row["Position"] || "",
          department: row["Branch"] || "",
          contact: row["Phone No."] || "",
          systems: [row["Deactivate"] || "All systems & Communication channels"],
          reason: "Resignation/Termination",
          approverName: "HR Officer",
          approverJobTitle: "HR",
          approverContact: "N/A",
          subsidiary,
          status: "Pending",
        });

        console.log(`Created revocation entry for ${fullName}`);
        revocations.push(entry);
      } catch (error) {
        console.error(`Error creating revocation for ${fullName}:`, error);
      }
    }

    const emailBody = `
      <h3 style="margin-bottom: 8px;">User Access Revocation Notification</h3>
      <p>Dear IT Administrator,</p>
      <p>This is to formally notify you that <strong>${revocations.length}</strong> user(s) have been submitted by HR for system access revocation under <strong>${subsidiary}</strong>.</p>
      <p>Please review and take the necessary deactivation actions.</p>
      <p>For your convenience, the list of affected employees is attached in the Excel file.</p>
      <p>Regards,<br /><strong>HR Access Management System</strong></p>
    `;

    // Only try to send email if we have at least one revocation
    if (revocations.length > 0) {
      try {
        console.log("Attempting to send email notification for subsidiary:", subsidiary);

        // Get subsidiary-specific email configuration
        const emailConfig = subsidiaryEmailConfig[subsidiary] || subsidiaryEmailConfig.default;
        console.log(`Using email configuration for ${subsidiary}`);

        // Get the appropriate IT email receiver for this subsidiary
        const itReceiverEmail = emailConfig.itReceiverEmail;

        console.log(`Sending email to ${itReceiverEmail} for subsidiary ${subsidiary}`);

        // Convert the Excel file to base64 for attachment
        const fileContent = fs.readFileSync(filePath, { encoding: 'base64' });

        // Send email using the external email service
        await emailService.sendEmail({
          to: itReceiverEmail,
          subject: `New Revocation Submission from HR (${subsidiary})`,
          emailBody: emailBody,
          tenant: emailConfig.tenantId,
          attachments: [
            {
              filename: req.file.originalname,
              content: fileContent,
              encoding: 'base64',
              contentType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            }
          ]
        });

        console.log("Email notification sent successfully to", itReceiverEmail);
      } catch (emailError) {
        console.error("Failed to send email notification:", emailError);
        // Continue processing even if email fails
      }
    } else {
      console.log("Skipping email notification: No revocations to process");
    }

    // Return success response
    res.status(200).json({
      message: `Bulk upload successful. ${revocations.length} revocation(s) processed.`,
      count: revocations.length,
      subsidiary: subsidiary
    });
  } catch (error) {
    console.error("Bulk upload error:", error);
    // Provide more detailed error message
    const errorMessage = error.message || "Failed to process bulk upload";
    res.status(500).json({
      error: errorMessage,
      details: error.stack
    });
  }
};

module.exports = {
  submitRevocation,
  getPendingRevocations,
  approveRevocationHR,
  approveRevocation,
  rejectRevocation,
  getAllRevocations,
  getCompletedRevocations,
  bulkUploadRevocations,
};
