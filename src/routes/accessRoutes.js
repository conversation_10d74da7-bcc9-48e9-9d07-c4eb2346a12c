const express = require("express");
const axios = require("axios");
const multer = require("multer");
const router = express.Router();
const upload = multer({ storage: multer.memoryStorage() });

const auditTrail = require("../_middlewares/auditTrail");
const populateAuditData = require("../_middlewares/populateAuditData");
const { UserAccessRequest } = require("../db/models");
const {
  approveBranchHead,
  approveHR,
  approveExecutive,
  approveIT,
  rejectAccessRequest,
  dataMiddleware,
  generateAccessRequestReport,
} = require("../controllers/accessFormController");
const { sendEmail } = require("#src/utils/emailService");

const approverEmails = require("../config/approverEmails");

require("dotenv").config();

const SUBSIDIARY_CONFIG = {
  platinumkenya: {
    authUrl: "https://ws.platinumcredit.co.ke/ws/auth/gettoken",
    apiUrl: "https://ws.platinumcredit.co.ke/api/dimension",
    origin: "https://ws.platinumcredit.co.ke",
  },
  premierkenya: {
    authUrl: "https://cache.premiergroup.co.ke/ws/auth/gettoken",
    apiUrl: "https://cache.premiergroup.co.ke/api/dimension",
    origin: "https://cache.premiergroup.co.ke",
  },
  momentumcredit: {
    authUrl: "https://ws.momentumcredit.co.ke/ws/auth/gettoken",
    apiUrl: "https://ws.momentumcredit.co.ke/api/dimension",
    origin: "https://ws.momentumcredit.co.ke",
  },
  platinumtanzania: {
    authUrl: "https://ws.platinumcredit.co.tz/ws/auth/gettoken",
    apiUrl: "https://ws.platinumcredit.co.tz/api/dimension",
    origin: "https://ws.platinumcredit.co.tz",
  },
  premierfanikiwa: {
    authUrl: "https://ws.fmfc.co.tz/ws/auth/gettoken",
    apiUrl: "https://ws.fmfc.co.tz/api/dimension",
    origin: "https://ws.fmfc.co.tz",
  },
  spectrumzambia: {
    authUrl: "https://cache.spectrumcreditltd.com/ws/auth/gettoken",
    apiUrl: "https://cache.spectrumcreditltd.com/api/dimension",
    origin: "https://cache.spectrumcreditltd.com",
  },
  premiersouthafrica: {
    authUrl: "https://ws.premiercredit.co.za/ws/auth/gettoken",
    apiUrl: "https://ws.premiercredit.co.za/api/dimension",
    origin: "https://ws.premiercredit.co.za",
  },
  platinumuganda: {
    authUrl: "https://ws.platinumcredit.co.ug/ws/auth/gettoken",
    apiUrl: "https://ws.platinumcredit.co.ug/api/dimension",
    origin: "https://ws.platinumcredit.co.ug",
  },
  premieruganda: {
    authUrl: "https://ws.premiercredit.co.ug/ws/auth/gettoken",
    apiUrl: "https://ws.premiercredit.co.ug/api/dimension",
    origin: "https://ws.premiercredit.co.ug",
  },
};

const getSubsidiaryFromHeader = (req) =>
  req.headers["x-subsidiary"]?.toLowerCase() || "platinumkenya";

const getApiToken = async (subsidiaryKey) => {
  const config = SUBSIDIARY_CONFIG[subsidiaryKey];
  if (!config) throw new Error(`Unknown subsidiary: ${subsidiaryKey}`);

  try {
    const response = await axios.post(
      config.authUrl,
      process.env.DIMENSION_API_TOKEN_PLATKE,
      {
        headers: {
          Authorization: `Bearer ${process.env.DIMENSION_API_TOKEN_PLATKE}`,
          Accept: "application/json",
          "Content-Type": "application/json",
          Origin: config.origin,
        },
      }
    );
    return {
      token: response.data,
      origin: config.origin,
      apiUrl: config.apiUrl,
    };
  } catch (error) {
    console.error(
      "Error fetching token:",
      error?.response?.data || error.message
    );
    throw new Error("Failed to fetch API token");
  }
};

const fetchDimensionData = async (req, res, endpoint, entityName) => {
  const subsidiary = getSubsidiaryFromHeader(req);
  try {
    console.log(`Fetching ${entityName} for subsidiary: ${subsidiary}`);

    // Get API token and configuration
    const { token, origin, apiUrl } = await getApiToken(subsidiary);

    if (!apiUrl) {
      console.error(`Missing apiUrl for subsidiary: ${subsidiary}`);
      return res.status(500).json({
        error: `Configuration error: Missing apiUrl for ${subsidiary}`,
        subsidiary: subsidiary
      });
    }

    console.log(`Making request to: ${apiUrl}/${endpoint}`);

    const response = await axios.get(`${apiUrl}/${endpoint}`, {
      headers: {
        Authorization: `Bearer ${token}`,
        Accept: "application/json",
        "Content-Type": "application/json",
        Origin: origin,
      },
    });

    console.log(`Successfully fetched ${entityName} for ${subsidiary}`);
    return res.json(response.data);
  } catch (error) {
    // Log detailed error information
    console.error(`Error fetching ${entityName} for ${subsidiary}:`);
    console.error(`Request URL: ${SUBSIDIARY_CONFIG[subsidiary]?.apiUrl}/${endpoint}`);
    console.error(`Error details:`, error?.response?.data || error.message);

    if (error.code === 'ECONNREFUSED') {
      console.error(`Connection refused to ${SUBSIDIARY_CONFIG[subsidiary]?.apiUrl}`);
      return res.status(500).json({
        error: `Connection refused to ${subsidiary} API server`,
        details: error.message,
        subsidiary: subsidiary
      });
    }

    return res.status(500).json({
      error: `Failed to fetch ${entityName}`,
      details: error.message,
      subsidiary: subsidiary
    });
  }
};

router.get("/branches", (req, res) =>
  fetchDimensionData(req, res, "branches", "branches")
);
router.get("/userroles", (req, res) =>
  fetchDimensionData(req, res, "roles", "roles")
);
router.get("/departments", (req, res) =>
  fetchDimensionData(req, res, "departments", "departments")
);

router.post(
  "/",
  upload.fields([
    { name: "cv" },
    { name: "id" },
    { name: "kraPin" },
    { name: "nssf" },
    { name: "sha" },
    { name: "bankDetails" },
    { name: "passportImage" },
    { name: "contract" },
    { name: "localGovId" },
    { name: "refereesLetter" },
    { name: "personalInfoForm" },
    { name: "passportSizes" },
    { name: "applicationLetter" },
    { name: "tin" },
  ]),
  populateAuditData,
  auditTrail("user-request-access"),
  async (req, res) => {
    try {
      const subId = getSubsidiaryFromHeader(req);
      let { accessType, ...otherFields } = req.body;
      if (!Array.isArray(accessType)) accessType = [accessType];

      const request = await UserAccessRequest.create({
        ...otherFields,
        accessType,
        subId,
      });

      const {
        firstName,
        lastName,
        email,
        systemName,
        branch,
        department,
        telephone,
        role,
        reason,
      } = req.body;

      const attachments = [];
      if (req.files) {
        for (const [key, files] of Object.entries(req.files)) {
          const file = files[0];
          attachments.push({
            filename: file.originalname,
            content: file.buffer.toString("base64"),
            encoding: "base64",
            contentType: file.mimetype || "application/octet-stream",
          });
        }
      }

      const approvers = approverEmails[subId];

      if (!approvers) {
        return res.status(400).json({ error: "Invalid subsidiary" });
      }

      sendEmail({
        to: approvers.HR,
        subject: "New Access Request Pending Approval",
        tenant: subId,
        emailBody: `
          <p>Dear HR,</p>
          <p>A new access request has been submitted and is awaiting your approval.</p>
          <table style="border-collapse: collapse; width: auto;">
            <tr><td style="font-weight: bold;">Requestor:</td><td>${firstName} ${lastName}</td></tr>
            <tr><td style="font-weight: bold;">Email:</td><td><a href="mailto:${email}">${email}</a></td></tr>
            <tr><td style="font-weight: bold;">System Name:</td><td>${systemName}</td></tr>
            <tr><td style="font-weight: bold;">Branch:</td><td>${branch}</td></tr>
            <tr><td style="font-weight: bold;">Department:</td><td>${department}</td></tr>
            <tr><td style="font-weight: bold;">Access Type:</td><td>${accessType.join(", ")}</td></tr>
            <tr><td style="font-weight: bold;">Role:</td><td>${role}</td></tr>
            <tr><td style="font-weight: bold;">Reason:</td><td>${reason || "N/A"}</td></tr>
          </table>
          <p><a href="https://uat-uap.platcorpgroup.com/user-access/ui/approval/${subId}" target="_blank">Click here to review and approve this request</a></p>
        `,
        tenant: subId,
        // attachments,
      });

      return res.status(201).json(request);
    } catch (error) {
      console.error("Error Creating Access Request:", error);
      return res.status(500).json({
        error: "Internal Server Error",
        details: error.message,
      });
    }
  }
);

// Report endpoint for auditors and IT personnel
router.get("/report", generateAccessRequestReport);

router.get("/", async (req, res) => {
  try {
    const subId = getSubsidiaryFromHeader(req);
    const requests = await UserAccessRequest.findAll({ where: { subId } });
    return res.json(requests);
  } catch (error) {
    console.error("Error Fetching Requests:", error);
    return res.status(500).json({ error: "Internal Server Error" });
  }
});

router.get("/:id", async (req, res) => {
  try {
    const request = await UserAccessRequest.findByPk(req.params.id);
    if (!request) return res.status(404).json({ error: "Request Not Found" });
    return res.json(request);
  } catch (error) {
    console.error("Error Fetching Request:", error);
    return res.status(500).json({ error: "Internal Server Error" });
  }
});

router.put("/:id", async (req, res) => {
  try {
    const request = await UserAccessRequest.findByPk(req.params.id);
    if (!request) return res.status(404).json({ error: "Request Not Found" });
    await request.update(req.body);
    return res.json(request);
  } catch (error) {
    console.error("Error Updating Request:", error);
    return res.status(500).json({ error: "Internal Server Error" });
  }
});

router.delete("/:id", async (req, res) => {
  try {
    const request = await UserAccessRequest.findByPk(req.params.id);
    if (!request) return res.status(404).json({ error: "Request Not Found" });
    await request.destroy();
    return res.json({ message: "Request Deleted Successfully" });
  } catch (error) {
    console.error("Error Deleting Request:", error);
    return res.status(500).json({ error: "Internal Server Error" });
  }
});

router.put(
  "/:id/approve-branch-head",
  dataMiddleware,
  auditTrail("branch-head-approval"),
  approveBranchHead
);
router.put(
  "/:id/approve-hr",
  dataMiddleware,
  auditTrail("hr-approval"),
  approveHR
);
router.put(
  "/:id/approve-executive",
  dataMiddleware,
  auditTrail("executive-approval"),
  approveExecutive
);
router.put(
  "/:id/approve-it",
  dataMiddleware,
  auditTrail("IT-approval"),
  approveIT
);
router.put("/:id/reject", rejectAccessRequest, auditTrail("rejection"));

module.exports = router;
