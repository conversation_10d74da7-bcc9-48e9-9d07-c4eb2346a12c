const express = require("express");
const router = express.Router();

const { uploadSignature, uploadExcel } = require("../_middlewares/upload");
const roleCheck = require("../_middlewares/roleCheck");

const {
  submitRevocation,
  getPendingRevocations,
  approveRevocationHR,
  approveRevocation,
  rejectRevocation,
  getAllRevocations,
  getCompletedRevocations,
  bulkUploadRevocations,
} = require("../controllers/revocationFormController");

// HR submits revocation form with signature (single) - Only HR, IT, and systemAdmin can submit
router.post("/submit", roleCheck(['hr', 'systemAdmin', 'it']), uploadSignature.single("signature"), submitRevocation);

// HR bulk uploads Excel file (with error handling) - Only HR, IT, and systemAdmin can bulk upload
router.post("/bulk-upload", roleCheck(['hr', 'systemAdmin', 'it']), uploadExcel, bulkUploadRevocations);

// Filtered: Get all pending revocations for a specific subsidiary - Only IT and systemAdmin can view
router.get("/pending", roleCheck(['it', 'systemAdmin']), (req, res) => {
  const subsidiary = req.headers["x-subsidiary"];
  getPendingRevocations(req, res, subsidiary);
});

// HR approves specific revocation - Only HR and systemAdmin can approve at HR level
router.put("/:id/approve-hr", roleCheck(['hr', 'systemAdmin']), approveRevocationHR);

// IT approves specific revocation - Only IT and systemAdmin can approve
router.put("/:id/approve", roleCheck(['it', 'systemAdmin']), approveRevocation);

// IT rejects specific revocation - Only IT and systemAdmin can reject
router.put("/:id/reject", roleCheck(['it', 'systemAdmin']), rejectRevocation);

// Filtered: Get full audit trail per subsidiary - Only IT and systemAdmin can view audit
router.get("/audit", roleCheck(['it', 'systemAdmin']), (req, res) => {
  const subsidiary = req.headers["x-subsidiary"];
  getAllRevocations(req, res, subsidiary);
});

// Filtered: Get completed revocations for subsidiary - Only IT and systemAdmin can view completed
router.get("/completed", roleCheck(['it', 'systemAdmin']), (req, res) => {
  const subsidiary = req.headers["x-subsidiary"];
  getCompletedRevocations(req, res, subsidiary);
});

module.exports = router;
