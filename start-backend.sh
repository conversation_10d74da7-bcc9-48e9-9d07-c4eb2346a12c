#!/bin/bash

echo "Starting backend server..."
cd /home/<USER>/Platcorp-Projects/digital-user-access-system

# Kill any existing processes on port 7080 and 7081
echo "Killing existing processes..."
lsof -ti:7080 | xargs kill -9 2>/dev/null || true
lsof -ti:7081 | xargs kill -9 2>/dev/null || true

# Wait a moment
sleep 2

# Start the backend
echo "Starting backend on port 7080..."
PORT=7080 node src/index.js &

# Wait for it to start
sleep 5

# Test if it's running
if curl -s http://localhost:7080/user-access/v1/health >/dev/null 2>&1; then
    echo "✅ Backend started successfully on http://localhost:7080"
    echo "✅ Frontend is running on http://localhost:5173/user-access/ui"
    echo ""
    echo "🔑 LOGIN CREDENTIALS:"
    echo "URL: http://localhost:5173/user-access/ui/login/platinumuganda"
    echo "Email: <EMAIL>"
    echo "Password: Admin@123"
else
    echo "❌ Backend failed to start"
    echo "Trying alternative port 7081..."
    PORT=7081 node src/index.js &
    sleep 5
    if curl -s http://localhost:7081/user-access/v1/health >/dev/null 2>&1; then
        echo "✅ Backend started on http://localhost:7081"
        echo "⚠️  You may need to update frontend config to use port 7081"
    else
        echo "❌ Backend failed to start on both ports"
    fi
fi
