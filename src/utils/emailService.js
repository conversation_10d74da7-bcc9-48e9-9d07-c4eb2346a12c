// require("dotenv").config();
// const nodemailer = require("nodemailer");
// // Configure the SMTP transporter using Gmail
// const transporter = nodemailer.createTransport({
//   host: "smtp.gmail.com",
//   port: 587,
//   secure: false,
//   auth: {
//     user: process.env.EMAIL_USER, // <EMAIL>
//     pass: process.env.EMAIL_PASS, // Use an App Password, not a regular password
//   },
//   tls: {
//     rejectUnauthorized: false,
//   },
// });
// /**
//  * Send an email notification to the approver
//  * @param {string} recipientEmail - The recipient's email address
//  * @param {string} subject - The email subject
//  * @param {string} message - The email message body
//  */
// const sendEmailNotification = async (recipientEmail, subject, message) => {
//   try {
//     const mailOptions = {
//       from: `"Platinum Credit System" <${process.env.EMAIL_USER}>`, // Sender email
//       to: recipientEmail,
//       subject: subject,
//       text: message,
//       html: `<p>${message.replace(/\n/g, "<br>")}</p>`,
//     };

//     await transporter.sendMail(mailOptions);
//     console.log(`Email sent successfully to ${recipientEmail}`);
//   } catch (error) {
//     console.error(`Failed to send email to ${recipientEmail}:`, error);
//   }
// };

// // Ensure this export is correct
// module.exports = { sendEmailNotification };

const axios = require("axios");

const email = {
  loadApi: () => {
    axios.defaults.baseURL = process.env.EMAIL_API_URL;
    axios.defaults.headers.common["apiKey"] = process.env.EMAIL_API_KEY;
    return axios;
  },
  sendEmail: async ({
    to,
    subject,
    emailBody,
    tenant = "platinumKenya",
    attachments = [],
  }) => {
    const emailSent = await email.loadApi().post("/email", {
      to,
      subject,
      body: emailBody,
      tenantId: tenant,
      groupRecordId: "pkeUserAccess",
      attachments,
    },
    {
      headers: {
        "Content-Type": "application/json",
      },
      // timeout: 30000,
    });

    return emailSent;
  },
};

module.exports = email;
