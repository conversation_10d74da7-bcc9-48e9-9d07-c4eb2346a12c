<template>
  <div class="p-6 bg-white shadow-lg rounded-xl mt-20">
    <h2 class="text-2xl font-bold mb-6 text-gray-800">
      Access Removal Requests
    </h2>

    <div v-if="loading" class="text-gray-500">Loading revocations...</div>

    <div class="relative">
      <input
        v-model="search"
        placeholder="Search by name or ID..."
        class="mb-4 p-2 border border-gray-300 rounded w-1/3"
        @input="applyFilter"
      />
    </div>

    <table
      v-if="!loading"
      class="min-w-full bg-white shadow rounded-lg overflow-hidden"
    >
      <thead class="bg-gray-100 text-left text-sm">
        <tr>
          <th class="p-4">Full Name</th>
          <th class="p-4">Employee ID</th>
          <th class="p-4">Department</th>
          <th class="p-4">Systems</th>
          <th class="p-4">Status</th>
          <th class="p-4">Reason</th>
          <th class="p-4">Actions</th>
        </tr>
      </thead>
      <tbody>
        <tr
          v-for="request in paginatedRevocations"
          :key="request.id"
          class="border-t text-sm"
        >
          <td class="p-4">{{ request.fullName }}</td>
          <td class="p-4">{{ request.employeeId }}</td>
          <td class="p-4">{{ request.department }}</td>
          <td class="p-4">
            <ul class="list-disc ml-4">
              <li v-for="system in request.systems" :key="system">
                {{ system }}
              </li>
            </ul>
          </td>
          <td class="p-4">
            <span
              class="px-2 py-1 rounded text-xs font-semibold"
              :class="getStatusClass(request.status)"
            >
              {{ request.status }}
            </span>
          </td>
          <td class="p-4">{{ request.reason }}</td>
          <td class="p-4">
            <!-- HR Approval Button -->
            <button
              v-if="canApproveHR(request.status)"
              class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded mr-2"
              @click="approveHR(request.id)"
            >
              HR Approve
            </button>
            <!-- IT Approval Button -->
            <button
              v-if="canApproveIT(request.status)"
              class="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded mr-2"
              @click="
                confirmId = request.id;
                showConfirmModal = true;
              "
            >
              IT Approve
            </button>
            <!-- Reject Button -->
            <button
              v-if="canReject(request.status)"
              class="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded"
              @click="reject(request.id)"
            >
              Reject
            </button>
          </td>
        </tr>
      </tbody>
    </table>

    <div v-if="!loading && revocations.length === 0" class="text-gray-500 mt-6">
      No pending revocations.
    </div>

    <!-- Pagination Controls -->
    <div class="flex justify-between items-center mt-4">
      <div class="text-sm">Items per page:</div>
      <select
        v-model="itemsPerPage"
        class="border rounded p-1 text-sm ml-2"
        @change="currentPage = 1"
      >
        <option v-for="n in [5, 10, 20]" :key="n" :value="n">{{ n }}</option>
      </select>

      <div class="ml-auto flex items-center space-x-4">
        <button
          class="px-4 py-1 rounded bg-gray-400 text-white disabled:opacity-50"
          :disabled="currentPage === 1"
          @click="currentPage--"
        >
          ◀ Previous
        </button>
        <span class="text-sm">
          Page {{ currentPage }} of {{ totalPages }}
        </span>
        <button
          class="px-4 py-1 rounded bg-blue-900 text-white disabled:opacity-50"
          :disabled="currentPage === totalPages"
          @click="currentPage++"
        >
          Next ▶
        </button>
      </div>
    </div>
  </div>

  <!-- Confirm Approval Modal -->
  <div v-if="showConfirmModal" class="modal-backdrop">
    <div class="bg-white p-6 rounded-lg shadow-lg max-w-md w-full">
      <h3 class="text-lg font-bold mb-2">Confirm Approval</h3>
      <p class="mb-4">Are you sure you want to approve this revocation?</p>
      <div class="flex justify-end space-x-3">
        <button
          @click="approve(confirmId)"
          class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded"
        >
          Yes, Approve
        </button>
        <button
          @click="showConfirmModal = false"
          class="bg-gray-200 hover:bg-gray-300 text-black px-4 py-2 rounded"
        >
          Cancel
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import {
  getPendingRevocations,
  approveRevocationHR,
  approveRevocation,
  rejectRevocation,
} from "@/services/apiService";

export default {
  name: "AccessRemoval",
  props: ["subsidiary"],
  data() {
    return {
      revocations: [],
      loading: false,
      search: "",
      filteredRevocations: [],
      currentPage: 1,
      itemsPerPage: 10,
      showConfirmModal: false,
      confirmId: null,
    };
  },
  computed: {
    totalPages() {
      return Math.ceil(this.filteredRevocations.length / this.itemsPerPage);
    },
    paginatedRevocations() {
      const start = (this.currentPage - 1) * this.itemsPerPage;
      const end = start + this.itemsPerPage;
      return this.filteredRevocations.slice(start, end);
    },
  },
  methods: {
    async loadRevocations() {
      this.loading = true;
      try {
        const response = await getPendingRevocations(this.subsidiary);
        this.revocations = response.data || [];
        this.filteredRevocations = [...this.revocations];
      } catch (error) {
        console.error("Failed to fetch revocations", error);
      } finally {
        this.loading = false;
      }
    },
    applyFilter() {
      const query = this.search.toLowerCase();
      this.filteredRevocations = this.revocations.filter(
        (r) =>
          r.fullName.toLowerCase().includes(query) ||
          r.employeeId.toLowerCase().includes(query)
      );
      this.currentPage = 1;
    },
    async approveHR(id) {
      try {
        await approveRevocationHR(id, this.subsidiary);
        alert("Revocation approved by HR. Forwarded to IT for final approval.");
        await this.loadRevocations();
      } catch (error) {
        alert("Error approving revocation at HR level");
        console.error(error);
      }
    },
    async approve(id) {
      try {
        await approveRevocation(id, this.subsidiary);
        this.showConfirmModal = false;
        alert("Revocation fully approved by IT");
        await this.loadRevocations();
      } catch (error) {
        alert("Error approving revocation at IT level");
        console.error(error);
      }
    },
    async reject(id) {
      const reason = prompt("Enter rejection reason:");
      if (reason) {
        try {
          await rejectRevocation(id, reason, this.subsidiary);
          await this.loadRevocations();
        } catch (error) {
          alert("Error rejecting revocation");
        }
      }
    },
    getStatusClass(status) {
      return {
        'bg-yellow-100 text-yellow-700': status === 'Pending',
        'bg-green-100 text-green-700': status === 'Approved',
        'bg-red-100 text-red-700': status === 'Rejected',
      };
    },
    canApproveIT(status) {
      // IT can approve revocations directly when they are Pending
      // (Revocations don't need HR approval - HR submits them)
      return status === 'Pending';
    },
    canReject(status) {
      // Can reject if not already processed
      return !['Approved', 'Rejected'].includes(status);
    },
  },
  mounted() {
    this.loadRevocations();
  },
};
</script>

<style scoped>
th,
td {
  font-size: 0.95rem;
}
.modal-backdrop {
  position: fixed;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 50;
}
</style>
