/**
 * Subsidiary-specific email configuration
 * This file contains email settings for each subsidiary
 * For use with external email service
 */

require('dotenv').config();

module.exports = {
  // Platinum Kenya
  platinumkenya: {
    itReceiverEmail: process.env.IT_EMAIL_RECEIVER || '<EMAIL>',
    senderName: 'Platinum Kenya HR Portal',
    tenantId: 'platinumkenya'
  },

  // Premier Kenya
  premierkenya: {
    itReceiverEmail: process.env.PREMIER_IT_EMAIL_RECEIVER || '<EMAIL>',
    senderName: 'Premier Kenya HR Portal',
    tenantId: 'premierkenya'
  },

  // Momentum Credit
  momentumcredit: {
    itReceiverEmail: process.env.MOMENTUM_IT_EMAIL_RECEIVER || '<EMAIL>',
    senderName: 'Momentum Credit HR Portal',
    tenantId: 'momentumcredit'
  },

  // Platinum Tanzania
  platinumtanzania: {
    itReceiverEmail: process.env.PLATINUMTZ_IT_EMAIL_RECEIVER || '<EMAIL>',
    senderName: 'Platinum Tanzania HR Portal',
    tenantId: 'platinumtanzania'
  },

  // Premier Fanikiwa
  premierfanikiwa: {
    itReceiverEmail: process.env.FANIKIWA_IT_EMAIL_RECEIVER || '<EMAIL>',
    senderName: 'Premier Fanikiwa HR Portal',
    tenantId: 'premierfanikiwa'
  },

  // Platinum Uganda
  platinumuganda: {
    itReceiverEmail: process.env.PLATINUMUG_IT_EMAIL_RECEIVER || '<EMAIL>',
    senderName: 'Platinum Uganda HR Portal',
    tenantId: 'platinumuganda'
  },

  // Premier Uganda
  premieruganda: {
    itReceiverEmail: process.env.PREMIERUG_IT_EMAIL_RECEIVER || '<EMAIL>',
    senderName: 'Premier Uganda HR Portal',
    tenantId: 'premieruganda'
  },

  // Spectrum Zambia
  spectrumzambia: {
    itReceiverEmail: process.env.SPECTRUM_IT_EMAIL_RECEIVER || '<EMAIL>',
    senderName: 'Spectrum Zambia HR Portal',
    tenantId: 'spectrumzambia'
  },

  // Premier South Africa
  premiersouthafrica: {
    itReceiverEmail: process.env.PREMIERSA_IT_EMAIL_RECEIVER || '<EMAIL>',
    senderName: 'Premier South Africa HR Portal',
    tenantId: 'premiersouthafrica'
  },

  // Default fallback configuration (uses Platinum Kenya settings)
  default: {
    itReceiverEmail: process.env.IT_EMAIL_RECEIVER || '<EMAIL>',
    senderName: 'HR Portal',
    tenantId: 'platinumkenya'
  }
};
