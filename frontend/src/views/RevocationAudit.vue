<template>
  <div class="p-6 bg-white shadow-lg rounded-xl mt-20">
    <h2 class="text-2xl font-bold mb-6 text-gray-800">
      Revocation Audit Trail
    </h2>

    <div class="mb-4 flex justify-between items-center">
      <input
        v-model="searchTerm"
        type="text"
        placeholder="Search by name or employee ID..."
        class="border px-3 py-2 rounded shadow-sm w-1/3"
      />

      <select
        v-model="statusFilter"
        class="border px-3 py-2 rounded shadow-sm text-sm"
      >
        <option value="">All Statuses</option>
        <option value="Pending">Pending</option>
        <option value="Approved">Approved</option>
        <option value="Rejected">Rejected</option>
      </select>
    </div>

    <div v-if="loading" class="text-gray-500">Loading audit trail...</div>

    <table
      v-if="!loading"
      class="min-w-full bg-white shadow rounded-lg overflow-hidden text-sm"
    >
      <thead class="bg-gray-100 text-left">
        <tr>
          <th class="p-4">Full Name</th>
          <th class="p-4">Status</th>
          <th class="p-4">Submitted</th>
          <th class="p-4">Actioned By</th>
          <th class="p-4">Actioned At</th>
          <th class="p-4">Rejection Reason</th>
          <th class="p-4">Signature</th>
        </tr>
      </thead>
      <tbody>
        <tr
          v-for="r in paginated"
          :key="r.id"
          class="border-t hover:bg-gray-50 transition"
        >
          <td class="p-4">{{ r.fullName }}</td>
          <td class="p-4">
            <span
              class="px-2 py-1 rounded text-xs font-semibold"
              :class="{
                'bg-yellow-100 text-yellow-700': r.status === 'Pending',
                'bg-green-100 text-green-700': r.status === 'Approved',
                'bg-red-100 text-red-700': r.status === 'Rejected',
              }"
            >
              {{ r.status }}
            </span>
          </td>
          <td class="p-4">{{ formatDate(r.createdAt) }}</td>
          <td class="p-4">{{ r.actionedByIT || "-" }}</td>
          <td class="p-4">{{ formatDate(r.actionedAt) || "-" }}</td>
          <td class="p-4">{{ r.rejectionReason || "-" }}</td>
          <td class="p-4">
            <a
              v-if="r.signaturePath"
              :href="getSignatureUrl(r.signaturePath)"
              target="_blank"
              class="text-blue-600 underline"
            >
              View
            </a>
            <span v-else>-</span>
          </td>
        </tr>
      </tbody>
    </table>

    <div
      v-if="!loading && filtered.length === 0"
      class="text-gray-500 mt-6 text-center"
    >
      No revocation records found.
    </div>

    <div class="flex justify-between items-center mt-6">
      <div class="text-sm text-gray-600">
        Showing {{ startIndex + 1 }}–{{ Math.min(endIndex, filtered.length) }}
        of
        {{ filtered.length }}
      </div>

      <div class="flex items-center space-x-4">
        <button
          :disabled="currentPage === 1"
          @click="currentPage--"
          class="px-4 py-2 text-white bg-gray-400 hover:bg-gray-500 rounded shadow disabled:opacity-50 disabled:cursor-not-allowed"
        >
          ◀ Previous
        </button>
        <span class="text-sm text-gray-700"
          >Page {{ currentPage }} of {{ totalPages }}</span
        >
        <button
          :disabled="currentPage === totalPages"
          @click="currentPage++"
          class="px-4 py-2 text-white bg-indigo-600 hover:bg-indigo-700 rounded shadow disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Next ▶
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { getAllRevocations } from "@/services/apiService";
import config from "@/config";

export default {
  name: "RevocationAudit",
  props: ["subsidiary"],
  data() {
    return {
      revocations: [],
      loading: false,
      searchTerm: "",
      statusFilter: "",
      currentPage: 1,
      itemsPerPage: 10,
    };
  },
  computed: {
    filtered() {
      return this.revocations.filter((r) => {
        const matchesSearch =
          r.fullName.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
          r.employeeId?.toLowerCase().includes(this.searchTerm.toLowerCase());
        const matchesStatus =
          !this.statusFilter || r.status === this.statusFilter;
        return matchesSearch && matchesStatus;
      });
    },
    totalPages() {
      return Math.ceil(this.filtered.length / this.itemsPerPage);
    },
    startIndex() {
      return (this.currentPage - 1) * this.itemsPerPage;
    },
    endIndex() {
      return this.startIndex + this.itemsPerPage;
    },
    paginated() {
      return this.filtered.slice(this.startIndex, this.endIndex);
    },
  },
  async mounted() {
    this.loading = true;
    try {
      const response = await getAllRevocations(this.subsidiary);
      this.revocations = response.data || [];
    } catch (error) {
      console.error("Error loading audit records", error);
    } finally {
      this.loading = false;
    }
  },
  methods: {
    formatDate(dateStr) {
      if (!dateStr) return null;
      return new Date(dateStr).toLocaleString();
    },
    getSignatureUrl(path) {
      return `${config["BACKEND_SERVICE"]}/${path}`;
    },
  },
};
</script>

<style scoped>
th,
td {
  font-size: 0.95rem;
}
</style>
