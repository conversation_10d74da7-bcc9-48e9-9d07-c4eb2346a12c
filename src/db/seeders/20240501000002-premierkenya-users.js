'use strict';
const bcrypt = require('bcryptjs');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Get the premierkenya tenant ID
    const tenants = await queryInterface.sequelize.query(
      `SELECT id FROM tenant WHERE sub = 'premierkenya'`,
      { type: Sequelize.QueryTypes.SELECT }
    );

    if (tenants.length === 0) {
      console.log('Premier Kenya tenant not found. Please run the tenant seeder first.');
      return;
    }

    const tenantId = tenants[0].id;

    // Hash passwords
    const salt = bcrypt.genSaltSync(10);
    const hashPassword = (password) => bcrypt.hashSync(password, salt);

    // Sample users for premierkenya
    const users = [
      {
        username: 'admin',
        email: '<EMAIL>',
        password: hashPassword('Admin@123'),
        sub: 'premierkenya',
        subId: tenantId.toString(),
        employerId: 'EMP001',
        role: 'systemAdmin',
        status: 'active',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        username: '<PERSON>',
        email: '<EMAIL>',
        password: hashPassword('Password@123'),
        sub: 'premierkenya',
        subId: tenantId.toString(),
        employerId: 'EMP005',
        role: 'systemAdmin',
        status: 'active',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        username: 'hr',
        email: '<EMAIL>',
        password: hashPassword('Hr@123'),
        sub: 'premierkenya',
        subId: tenantId.toString(),
        employerId: 'EMP002',
        role: 'hr',
        status: 'active',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        username: 'it',
        email: '<EMAIL>',
        password: hashPassword('It@123'),
        sub: 'premierkenya',
        subId: tenantId.toString(),
        employerId: 'EMP003',
        role: 'it',
        status: 'active',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        username: 'supervisor',
        email: '<EMAIL>',
        password: hashPassword('Supervisor@123'),
        sub: 'premierkenya',
        subId: tenantId.toString(),
        employerId: 'EMP004',
        role: 'supervisor',
        status: 'active',
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];

    // Check for existing users to avoid duplicates
    for (const user of users) {
      console.log(`Checking if user ${user.username} (${user.email}) exists...`);

      const existingUser = await queryInterface.sequelize.query(
        `SELECT * FROM "user" WHERE email = '${user.email}'`,
        { type: Sequelize.QueryTypes.SELECT }
      );

      if (existingUser.length === 0) {
        console.log(`User ${user.username} does not exist, creating now...`);
        try {
          await queryInterface.bulkInsert('user', [user]);
          console.log(`✅ User ${user.username} (${user.email}) created successfully for Premier Kenya`);
        } catch (error) {
          console.error(`❌ Error creating user ${user.username} (${user.email}):`, error.message);
        }
      } else {
        console.log(`⚠️ User ${user.username} (${user.email}) already exists for Premier Kenya, skipping creation`);

        // Optionally update the existing user
        try {
          await queryInterface.sequelize.query(
            `UPDATE "user" SET
              sub = 'premierkenya',
              subId = '${tenantId}',
              role = '${user.role}',
              status = '${user.status}'
            WHERE email = '${user.email}'`
          );
          console.log(`✅ User ${user.username} (${user.email}) updated successfully`);
        } catch (error) {
          console.error(`❌ Error updating user ${user.username} (${user.email}):`, error.message);
        }
      }
    }
  },

  async down(queryInterface, Sequelize) {
    // Get the premierkenya tenant ID
    const tenants = await queryInterface.sequelize.query(
      `SELECT id FROM tenant WHERE sub = 'premierkenya'`,
      { type: Sequelize.QueryTypes.SELECT }
    );

    if (tenants.length === 0) {
      return;
    }

    const tenantId = tenants[0].id;

    // Delete the users for premierkenya
    await queryInterface.bulkDelete('user', {
      subId: tenantId.toString()
    });
  }
};
