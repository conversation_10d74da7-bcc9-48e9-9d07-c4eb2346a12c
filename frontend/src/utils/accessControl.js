/**
 * Role-based access control configuration
 * Defines which roles can access which pages/features
 */
const accessMap = {
  // Pages - Core access
  'dashboard': ['systemAdmin', 'hr', 'it', 'supervisor'],
  'request-form': ['systemAdmin', 'hr', 'it', 'supervisor'], // Access form - all roles
  'approval': ['systemAdmin', 'hr', 'it'], // Approval flow - HR, IT, and systemAdmin
  'audit': ['systemAdmin', 'it'], // Audit trail - IT and systemAdmin only
  'reports': ['systemAdmin', 'it'], // Reports - IT and systemAdmin only
  'users': ['systemAdmin', 'it'], // User management - IT and systemAdmin only
  'tenants': ['systemAdmin'], // Only systemAdmin can access tenant management

  // Revocation pages
  'revoke-form': ['systemAdmin', 'it', 'supervisor'],
  'access-removal': ['systemAdmin', 'it'],
  'revocation-audit': ['systemAdmin', 'it'],
  'revoked-users-report': ['systemAdmin', 'it'],

  // User management specific features
  'users-create': ['systemAdmin', 'it'],
  'users-edit': ['systemAdmin', 'it'],
  'users-delete': ['systemAdmin', 'it'],
  'users-change-password': ['systemAdmin', 'it'],
  'users-change-status': ['systemAdmin', 'it'],
};

/**
 * Check if a user has access to a specific page/feature
 *
 * @param {string} resource - The page or feature to check access for
 * @param {object} user - The user object containing role information
 * @returns {boolean} - Whether the user has access
 */
export const canAccess = (resource, user) => {
  console.log(`Checking access for resource: ${resource}, user:`, user);

  // If no user or inactive user, no access
  if (!user || user.status === 'inactive') {
    console.warn('No user or inactive user, denying access');
    return false;
  }

  // If resource doesn't exist in the access map, deny access
  if (!accessMap[resource]) {
    console.warn(`Resource "${resource}" not found in access map`);
    return false;
  }

  // Check if user's role is allowed to access the resource
  const hasAccess = accessMap[resource].includes(user.role);
  console.log(`User role: ${user.role}, has access to ${resource}: ${hasAccess}`);
  return hasAccess;
};

/**
 * Get all accessible resources for a user
 *
 * @param {object} user - The user object containing role information
 * @returns {string[]} - Array of resources the user can access
 */
export const getAccessibleResources = (user) => {
  // If no user or inactive user, no access to anything
  if (!user || user.status === 'inactive') {
    return [];
  }

  // Return all resources the user can access
  return Object.keys(accessMap).filter(resource =>
    accessMap[resource].includes(user.role)
  );
};

export default {
  canAccess,
  getAccessibleResources
};
